#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to switch CSP from Report-Only mode to enforcement mode
 * Run this after testing the CSP in Report-Only mode and verifying no breakages
 */

const fs = require('fs');
const path = require('path');

const configPath = path.join(__dirname, '..', 'next.config.js');

try {
  let configContent = fs.readFileSync(configPath, 'utf8');
  
  // Replace Content-Security-Policy-Report-Only with Content-Security-Policy
  const updatedContent = configContent.replace(
    'Content-Security-Policy-Report-Only',
    'Content-Security-Policy'
  );
  
  // Update the comment as well
  const finalContent = updatedContent.replace(
    '// NOTE: Currently using Content-Security-Policy-Report-Only for testing\n// After verifying no breakages, change to \'Content-Security-Policy\' to enforce',
    '// NOTE: CSP is now in enforcement mode\n// Previously tested in Report-Only mode to ensure no breakages'
  );
  
  fs.writeFileSync(configPath, finalContent);
  
  console.log('✅ Successfully switched CSP from Report-Only to enforcement mode!');
  console.log('📝 Updated next.config.js');
  console.log('🔄 Please restart your Next.js development server to apply changes');
  
} catch (error) {
  console.error('❌ Error updating CSP configuration:', error.message);
  process.exit(1);
}
