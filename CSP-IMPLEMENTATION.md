# Content Security Policy (CSP) Implementation

## Overview

This document describes the Content Security Policy implementation for the OAP Frontend application to address the "CSP: Notices" security flag from RoboShadow.

## Current Implementation

### CSP Header Configuration

The CSP is currently configured in **Report-Only mode** in `next.config.js`:

```javascript
Content-Security-Policy-Report-Only: default-src 'self'; script-src 'self'; style-src 'self'; img-src 'self'; font-src 'self'; frame-ancestors 'self'; upgrade-insecure-requests
```

### Security Directives Explained

- `default-src 'self'` - Only allow resources from the same origin by default
- `script-src 'self'` - Only allow scripts from the same origin
- `style-src 'self'` - Only allow stylesheets from the same origin
- `img-src 'self'` - Only allow images from the same origin
- `font-src 'self'` - Only allow fonts from the same origin
- `frame-ancestors 'self'` - Prevent the page from being embedded in frames from other origins
- `upgrade-insecure-requests` - Automatically upgrade HTTP requests to HTTPS

## Testing Phase

### Current Status: Report-Only Mode

The CSP is currently in **Report-Only mode**, which means:
- ✅ CSP header is sent with all HTTP responses
- ✅ Violations are logged in browser console
- ❌ Violations are NOT blocked (content still loads)

### Testing Instructions

1. **Start the development server:**
   ```bash
   npm run dev
   ```

2. **Run the testing guide:**
   ```bash
   node scripts/test-csp.js
   ```

3. **Test all application pages:**
   - Navigate through all pages of the application
   - Check browser console for CSP violation reports
   - Look for any blocked resources or warnings

4. **Monitor for violations:**
   - Open browser Developer Tools (F12)
   - Go to Console tab
   - Look for messages containing "Content Security Policy"

## Switching to Enforcement Mode

### After Testing (No Violations Found)

Once you've verified that there are no CSP violations in Report-Only mode:

1. **Run the enforcement script:**
   ```bash
   node scripts/enable-csp-enforcement.js
   ```

2. **Restart your Next.js server:**
   ```bash
   npm run dev
   ```

3. **Verify enforcement:**
   - The CSP header will change from `Content-Security-Policy-Report-Only` to `Content-Security-Policy`
   - Violations will now be blocked instead of just reported

## Troubleshooting Common Issues

### Inline Styles/Scripts
If you encounter violations for inline styles or scripts:
- Move inline CSS to external stylesheet files
- Move inline JavaScript to external JS files
- Use Next.js built-in CSS and JS optimization features

### External Resources
If you need to load resources from external domains:
- Update the CSP directives in `next.config.js`
- Add specific domains to the appropriate directive (e.g., `img-src`, `font-src`)

### Third-party Services
For third-party services (analytics, CDNs, etc.):
- Identify the domains used by these services
- Add them to the appropriate CSP directives
- Consider using `nonce` or `hash` for trusted inline scripts

## Files Modified

- `next.config.js` - Main CSP configuration
- `scripts/test-csp.js` - Testing guide script
- `scripts/enable-csp-enforcement.js` - Script to switch to enforcement mode
- `CSP-IMPLEMENTATION.md` - This documentation

## Security Benefits

✅ **Prevents XSS attacks** by controlling script execution
✅ **Prevents data injection** by controlling resource loading
✅ **Prevents clickjacking** with frame-ancestors directive
✅ **Enforces HTTPS** with upgrade-insecure-requests
✅ **Reduces attack surface** by limiting allowed resource origins

## Next Steps

1. Test the application thoroughly in Report-Only mode
2. Address any CSP violations found during testing
3. Switch to enforcement mode using the provided script
4. Monitor production logs for any unexpected violations
